using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;
using System.Windows;
using InventoryManagement.DataAccess;
using InventoryManagement.Models;
using InventoryManagement.Infrastructure.Configuration;

namespace InventoryManagement.Infrastructure.Database
{
    /// <summary>
    /// Handles first-time database setup and initialization for user-friendly experience
    /// </summary>
    public class DatabaseSetupWizard
    {
        private readonly ILogger<DatabaseSetupWizard> _logger;
        private readonly EmbeddedPostgreSQLManager _postgresManager;
        private readonly ApplicationDbContext _dbContext;
        private readonly AppConfiguration _configuration;

        public DatabaseSetupWizard(
            ILogger<DatabaseSetupWizard> logger,
            EmbeddedPostgreSQLManager postgresManager,
            ApplicationDbContext dbContext,
            AppConfiguration configuration)
        {
            _logger = logger;
            _postgresManager = postgresManager;
            _dbContext = dbContext;
            _configuration = configuration;
        }

        /// <summary>
        /// Performs complete first-time setup with user-friendly progress indication
        /// </summary>
        public async Task<bool> RunFirstTimeSetupAsync()
        {
            try
            {
                _logger.LogInformation("Starting first-time database setup...");

                // Show setup progress window
                var progressWindow = new DatabaseSetupProgressWindow();
                progressWindow.Show();

                try
                {
                    // Step 1: Check PostgreSQL availability
                    progressWindow.UpdateProgress("Checking PostgreSQL availability...", 10);
                    if (!_postgresManager.IsPostgreSQLAvailable())
                    {
                        var result = MessageBox.Show(
                            "PostgreSQL binaries are required but not found.\n\n" +
                            "Would you like to download and install PostgreSQL automatically?\n\n" +
                            "This will download a portable version that doesn't require system installation.",
                            "PostgreSQL Required",
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Question);

                        if (result == MessageBoxResult.Yes)
                        {
                            progressWindow.UpdateProgress("Downloading PostgreSQL...", 20);
                            if (!await DownloadPortablePostgreSQLAsync(progressWindow))
                            {
                                throw new Exception("Failed to download PostgreSQL");
                            }
                        }
                        else
                        {
                            throw new Exception("PostgreSQL is required for the application to work");
                        }
                    }

                    // Step 2: Initialize database cluster
                    progressWindow.UpdateProgress("Initializing database cluster...", 30);
                    if (!await _postgresManager.InitializeDatabaseAsync())
                    {
                        throw new Exception("Failed to initialize PostgreSQL database cluster");
                    }

                    // Step 3: Start PostgreSQL
                    progressWindow.UpdateProgress("Starting PostgreSQL server...", 50);
                    if (!await _postgresManager.StartPostgreSQLAsync())
                    {
                        throw new Exception("Failed to start PostgreSQL server");
                    }

                    // Step 4: Create application database
                    progressWindow.UpdateProgress("Creating application database...", 60);
                    await CreateApplicationDatabaseAsync();

                    // Step 5: Run migrations
                    progressWindow.UpdateProgress("Setting up database schema...", 70);
                    await _dbContext.Database.MigrateAsync();

                    // Step 6: Seed initial data
                    progressWindow.UpdateProgress("Setting up initial data...", 80);
                    await SeedInitialDataAsync();

                    // Step 7: Create default admin user
                    progressWindow.UpdateProgress("Creating default admin user...", 90);
                    await CreateDefaultUsersAsync();

                    // Step 8: Complete
                    progressWindow.UpdateProgress("Setup complete!", 100);
                    await Task.Delay(1000); // Show completion briefly

                    progressWindow.Close();

                    // Show success message
                    MessageBox.Show(
                        "Database setup completed successfully!\n\n" +
                        "Default login credentials:\n" +
                        "Username: admin\n" +
                        "Password: admin123\n\n" +
                        "Please change the default password after first login.",
                        "Setup Complete",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);

                    _logger.LogInformation("First-time database setup completed successfully");
                    return true;
                }
                finally
                {
                    progressWindow.Close();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "First-time database setup failed");
                
                MessageBox.Show(
                    $"Database setup failed: {ex.Message}\n\n" +
                    "Please check the logs for more details or contact support.",
                    "Setup Failed",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);

                return false;
            }
        }

        /// <summary>
        /// Downloads and extracts portable PostgreSQL
        /// </summary>
        private async Task<bool> DownloadPortablePostgreSQLAsync(DatabaseSetupProgressWindow progressWindow)
        {
            try
            {
                // This would download PostgreSQL portable version
                // For now, we'll simulate the process and show instructions
                
                progressWindow.UpdateProgress("Preparing PostgreSQL download...", 25);
                await Task.Delay(1000);

                // Show instructions for manual download
                var result = MessageBox.Show(
                    "Automatic download is not yet implemented.\n\n" +
                    "Please download PostgreSQL portable version manually:\n\n" +
                    "1. Download PostgreSQL portable from: https://www.enterprisedb.com/download-postgresql-binaries\n" +
                    "2. Extract to: [Application Folder]/PostgreSQL/\n" +
                    "3. Restart the application\n\n" +
                    "Would you like to open the download page?",
                    "Manual Download Required",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Information);

                if (result == MessageBoxResult.Yes)
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = "https://www.enterprisedb.com/download-postgresql-binaries",
                        UseShellExecute = true
                    });
                }

                return false; // For now, return false to indicate manual setup needed
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading PostgreSQL");
                return false;
            }
        }

        /// <summary>
        /// Creates the application database
        /// </summary>
        private async Task CreateApplicationDatabaseAsync()
        {
            try
            {
                var connectionString = _postgresManager.GetConnectionString("postgres");
                using var connection = new Npgsql.NpgsqlConnection(connectionString);
                await connection.OpenAsync();

                // Check if database exists
                using var command = new Npgsql.NpgsqlCommand(
                    "SELECT 1 FROM pg_database WHERE datname = 'TomGeneralTradingDB'", 
                    connection);
                
                var exists = await command.ExecuteScalarAsync();
                if (exists == null)
                {
                    // Create database
                    using var createCommand = new Npgsql.NpgsqlCommand(
                        "CREATE DATABASE \"TomGeneralTradingDB\" WITH ENCODING 'UTF8'", 
                        connection);
                    await createCommand.ExecuteNonQueryAsync();
                    
                    _logger.LogInformation("Application database created successfully");
                }
                else
                {
                    _logger.LogInformation("Application database already exists");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating application database");
                throw;
            }
        }

        /// <summary>
        /// Seeds initial data for the application
        /// </summary>
        private async Task SeedInitialDataAsync()
        {
            try
            {
                // Add default categories
                if (!await _dbContext.Categories.AnyAsync())
                {
                    var categories = new[]
                    {
                        new Category { Name = "Electronics", Description = "Electronic items and accessories" },
                        new Category { Name = "Clothing", Description = "Clothing and apparel" },
                        new Category { Name = "Food & Beverages", Description = "Food and drink items" },
                        new Category { Name = "Home & Garden", Description = "Home and garden supplies" },
                        new Category { Name = "Books & Media", Description = "Books, movies, and media" }
                    };

                    _dbContext.Categories.AddRange(categories);
                }

                // Add default locations
                if (!await _dbContext.Locations.AnyAsync())
                {
                    var locations = new[]
                    {
                        new Location { Name = "Main Store", Description = "Main retail floor" },
                        new Location { Name = "Basement Storage", Description = "Basement storage area" },
                        new Location { Name = "Back Office", Description = "Office storage area" }
                    };

                    _dbContext.Locations.AddRange(locations);
                }

                await _dbContext.SaveChangesAsync();
                _logger.LogInformation("Initial data seeded successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error seeding initial data");
                throw;
            }
        }

        /// <summary>
        /// Creates default user accounts
        /// </summary>
        private async Task CreateDefaultUsersAsync()
        {
            try
            {
                if (!await _dbContext.Users.AnyAsync())
                {
                    var users = new[]
                    {
                        new User 
                        { 
                            Username = "admin", 
                            PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"),
                            FullName = "System Administrator",
                            Email = "<EMAIL>",
                            Role = UserRole.Admin,
                            IsActive = true,
                            CreatedAt = DateTime.UtcNow
                        },
                        new User 
                        { 
                            Username = "cashier", 
                            PasswordHash = BCrypt.Net.BCrypt.HashPassword("cashier123"),
                            FullName = "Sales Cashier",
                            Email = "<EMAIL>",
                            Role = UserRole.Cashier,
                            IsActive = true,
                            CreatedAt = DateTime.UtcNow
                        },
                        new User 
                        { 
                            Username = "basement", 
                            PasswordHash = BCrypt.Net.BCrypt.HashPassword("basement123"),
                            FullName = "Basement Manager",
                            Email = "<EMAIL>",
                            Role = UserRole.BasementManager,
                            IsActive = true,
                            CreatedAt = DateTime.UtcNow
                        }
                    };

                    _dbContext.Users.AddRange(users);
                    await _dbContext.SaveChangesAsync();
                    
                    _logger.LogInformation("Default users created successfully");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating default users");
                throw;
            }
        }
    }
}
