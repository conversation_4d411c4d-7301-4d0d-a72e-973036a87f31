using System.ComponentModel;

namespace InventoryManagement.ViewModels.Shared
{
    /// <summary>
    /// Shared view model for item display across different dialogs and views
    /// </summary>
    public class ItemViewModel : INotifyPropertyChanged
    {
        private int _id;
        private string _name = string.Empty;
        private string _sku = string.Empty;
        private string _barcode = string.Empty;
        private string _category = string.Empty;
        private decimal _availableQuantity;
        private decimal _costPrice;
        private decimal _retailPrice;

        /// <summary>
        /// Item ID
        /// </summary>
        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged(nameof(Id));
                }
            }
        }

        /// <summary>
        /// Item name
        /// </summary>
        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value ?? string.Empty;
                    OnPropertyChanged(nameof(Name));
                }
            }
        }

        /// <summary>
        /// Stock Keeping Unit
        /// </summary>
        public string SKU
        {
            get => _sku;
            set
            {
                if (_sku != value)
                {
                    _sku = value ?? string.Empty;
                    OnPropertyChanged(nameof(SKU));
                }
            }
        }

        /// <summary>
        /// Item barcode
        /// </summary>
        public string Barcode
        {
            get => _barcode;
            set
            {
                if (_barcode != value)
                {
                    _barcode = value ?? string.Empty;
                    OnPropertyChanged(nameof(Barcode));
                }
            }
        }

        /// <summary>
        /// Item category
        /// </summary>
        public string Category
        {
            get => _category;
            set
            {
                if (_category != value)
                {
                    _category = value ?? string.Empty;
                    OnPropertyChanged(nameof(Category));
                }
            }
        }

        /// <summary>
        /// Available quantity in stock
        /// </summary>
        public decimal AvailableQuantity
        {
            get => _availableQuantity;
            set
            {
                if (_availableQuantity != value)
                {
                    _availableQuantity = value;
                    OnPropertyChanged(nameof(AvailableQuantity));
                }
            }
        }

        /// <summary>
        /// Cost price of the item
        /// </summary>
        public decimal CostPrice
        {
            get => _costPrice;
            set
            {
                if (_costPrice != value)
                {
                    _costPrice = value;
                    OnPropertyChanged(nameof(CostPrice));
                }
            }
        }

        /// <summary>
        /// Retail price of the item
        /// </summary>
        public decimal RetailPrice
        {
            get => _retailPrice;
            set
            {
                if (_retailPrice != value)
                {
                    _retailPrice = value;
                    OnPropertyChanged(nameof(RetailPrice));
                }
            }
        }

        /// <summary>
        /// Property changed event
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// Raises the PropertyChanged event
        /// </summary>
        /// <param name="propertyName">Name of the property that changed</param>
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// Creates a copy of this ItemViewModel
        /// </summary>
        /// <returns>A new ItemViewModel with the same values</returns>
        public ItemViewModel Clone()
        {
            return new ItemViewModel
            {
                Id = this.Id,
                Name = this.Name,
                SKU = this.SKU,
                Barcode = this.Barcode,
                Category = this.Category,
                AvailableQuantity = this.AvailableQuantity,
                CostPrice = this.CostPrice,
                RetailPrice = this.RetailPrice
            };
        }

        /// <summary>
        /// Returns a string representation of the item
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"{Name} ({SKU})";
        }
    }
}
