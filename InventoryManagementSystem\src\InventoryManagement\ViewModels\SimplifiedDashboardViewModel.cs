using InventoryManagement.Commands;
using InventoryManagement.Models;
using InventoryManagement.Services;
using Microsoft.Extensions.Logging;
using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;

namespace InventoryManagement.ViewModels
{
    /// <summary>
    /// ViewModel for the simplified, user-friendly main dashboard
    /// Focuses on ease of use and clear navigation
    /// </summary>
    public class SimplifiedDashboardViewModel : INotifyPropertyChanged
    {
        private readonly ILogger<SimplifiedDashboardViewModel> _logger;
        private readonly IInventoryService _inventoryService;
        private readonly ICustomerService _customerService;
        private readonly ISalesService _salesService;
        private readonly IUserFriendlyMessageService _messageService;
        
        // Dashboard statistics
        private int _totalItems;
        private int _lowStockItems;
        private decimal _todaySales;
        private int _totalCustomers;
        private DateTime _currentDateTime;
        private User _currentUser;

        public SimplifiedDashboardViewModel(
            ILogger<SimplifiedDashboardViewModel> logger,
            IInventoryService inventoryService,
            ICustomerService customerService,
            ISalesService salesService,
            IUserFriendlyMessageService messageService)
        {
            _logger = logger;
            _inventoryService = inventoryService;
            _customerService = customerService;
            _salesService = salesService;
            _messageService = messageService;
            
            InitializeCommands();
            LoadDashboardDataAsync();
            
            // Update current time
            _currentDateTime = DateTime.Now;
            
            // Start timer to update time every minute
            var timer = new System.Windows.Threading.DispatcherTimer();
            timer.Interval = TimeSpan.FromMinutes(1);
            timer.Tick += (s, e) => CurrentDateTime = DateTime.Now;
            timer.Start();
        }

        #region Properties

        public int TotalItems
        {
            get => _totalItems;
            set => SetProperty(ref _totalItems, value);
        }

        public int LowStockItems
        {
            get => _lowStockItems;
            set => SetProperty(ref _lowStockItems, value);
        }

        public decimal TodaySales
        {
            get => _todaySales;
            set => SetProperty(ref _todaySales, value);
        }

        public int TotalCustomers
        {
            get => _totalCustomers;
            set => SetProperty(ref _totalCustomers, value);
        }

        public DateTime CurrentDateTime
        {
            get => _currentDateTime;
            set => SetProperty(ref _currentDateTime, value);
        }

        public User CurrentUser
        {
            get => _currentUser ?? App.CurrentUser;
            set => SetProperty(ref _currentUser, value);
        }

        #endregion

        #region Commands

        public ICommand OpenPOSCommand { get; private set; }
        public ICommand OpenInventoryCommand { get; private set; }
        public ICommand OpenCustomersCommand { get; private set; }
        public ICommand OpenReportsCommand { get; private set; }
        public ICommand OpenSettingsCommand { get; private set; }
        public ICommand OpenHelpCommand { get; private set; }
        public ICommand QuickSaleCommand { get; private set; }
        public ICommand AddItemCommand { get; private set; }
        public ICommand StockCheckCommand { get; private set; }
        public ICommand BackupCommand { get; private set; }
        public ICommand LogoutCommand { get; private set; }

        #endregion

        #region Command Initialization

        private void InitializeCommands()
        {
            OpenPOSCommand = new RelayCommand(ExecuteOpenPOS);
            OpenInventoryCommand = new RelayCommand(ExecuteOpenInventory);
            OpenCustomersCommand = new RelayCommand(ExecuteOpenCustomers);
            OpenReportsCommand = new RelayCommand(ExecuteOpenReports);
            OpenSettingsCommand = new RelayCommand(ExecuteOpenSettings);
            OpenHelpCommand = new RelayCommand(ExecuteOpenHelp);
            QuickSaleCommand = new RelayCommand(ExecuteQuickSale);
            AddItemCommand = new RelayCommand(ExecuteAddItem);
            StockCheckCommand = new RelayCommand(ExecuteStockCheck);
            BackupCommand = new RelayCommand(ExecuteBackup);
            LogoutCommand = new RelayCommand(ExecuteLogout);
        }

        #endregion

        #region Command Implementations

        private void ExecuteOpenPOS()
        {
            try
            {
                _logger.LogInformation("Opening Point of Sale module");
                // Navigate to POS window
                var posWindow = new Views.PointOfSaleView();
                posWindow.Show();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening POS module");
                ShowErrorMessage("Unable to open Point of Sale. Please try again.");
            }
        }

        private void ExecuteOpenInventory()
        {
            try
            {
                _logger.LogInformation("Opening Inventory Management module");
                // Navigate to inventory window
                var inventoryWindow = new Views.InventoryDashboardView();
                inventoryWindow.Show();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening Inventory module");
                ShowErrorMessage("Unable to open Inventory Management. Please try again.");
            }
        }

        private void ExecuteOpenCustomers()
        {
            try
            {
                _logger.LogInformation("Opening Customer Management module");
                // Navigate to customer window
                var customerWindow = new Views.CustomerManagementView();
                customerWindow.Show();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening Customer module");
                ShowErrorMessage("Unable to open Customer Management. Please try again.");
            }
        }

        private void ExecuteOpenReports()
        {
            try
            {
                _logger.LogInformation("Opening Reports module");
                // Navigate to reports window
                var reportsWindow = new Views.Reports.ReportsDashboardView();
                reportsWindow.Show();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening Reports module");
                ShowErrorMessage("Unable to open Reports. Please try again.");
            }
        }

        private void ExecuteOpenSettings()
        {
            try
            {
                _logger.LogInformation("Opening Settings module");
                // Navigate to settings window
                var settingsWindow = new Views.Settings.HardwareSettingsView();
                settingsWindow.Show();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening Settings module");
                ShowErrorMessage("Unable to open Settings. Please try again.");
            }
        }

        private void ExecuteOpenHelp()
        {
            try
            {
                _logger.LogInformation("Opening Help system");
                // Show help dialog
                var helpDialog = new Views.Dialogs.HelpViewerDialog();
                helpDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening Help system");
                ShowErrorMessage("Unable to open Help. Please try again.");
            }
        }

        private void ExecuteQuickSale()
        {
            try
            {
                _logger.LogInformation("Starting Quick Sale");
                // Open POS with quick sale mode
                var posWindow = new Views.PointOfSaleView();
                // Set quick sale mode if supported
                posWindow.Show();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting Quick Sale");
                ShowErrorMessage("Unable to start Quick Sale. Please try again.");
            }
        }

        private void ExecuteAddItem()
        {
            try
            {
                _logger.LogInformation("Opening Add Item dialog");
                // Open add item dialog
                var addItemDialog = new Views.Dialogs.ProductDialog();
                addItemDialog.ShowDialog();
                
                // Refresh dashboard data after adding item
                LoadDashboardDataAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening Add Item dialog");
                ShowErrorMessage("Unable to add item. Please try again.");
            }
        }

        private void ExecuteStockCheck()
        {
            try
            {
                _logger.LogInformation("Opening Stock Check");
                // Open inventory with stock check focus
                var inventoryWindow = new Views.InventoryDashboardView();
                inventoryWindow.Show();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening Stock Check");
                ShowErrorMessage("Unable to open Stock Check. Please try again.");
            }
        }

        private void ExecuteBackup()
        {
            try
            {
                _logger.LogInformation("Starting backup process");
                // Open backup dialog
                var backupWindow = new Views.BackupRestoreWindow();
                backupWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting backup");
                ShowErrorMessage("Unable to start backup. Please try again.");
            }
        }

        private void ExecuteLogout()
        {
            try
            {
                _logger.LogInformation("User logging out");
                
                // Clear current user
                App.CurrentUser = null;
                
                // Close current window and show login
                var loginWindow = new Views.LoginWindow();
                loginWindow.Show();
                
                // Close current dashboard
                foreach (System.Windows.Window window in System.Windows.Application.Current.Windows)
                {
                    if (window is Views.SimplifiedDashboard)
                    {
                        window.Close();
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout");
                ShowErrorMessage("Error during logout. Please try again.");
            }
        }

        #endregion

        #region Data Loading

        private async void LoadDashboardDataAsync()
        {
            try
            {
                _logger.LogInformation("Loading dashboard data");

                // Load statistics in parallel for better performance
                var tasks = new[]
                {
                    LoadTotalItemsAsync(),
                    LoadLowStockItemsAsync(),
                    LoadTodaySalesAsync(),
                    LoadTotalCustomersAsync()
                };

                await Task.WhenAll(tasks);

                _logger.LogInformation("Dashboard data loaded successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading dashboard data");
                ShowErrorMessage("Unable to load dashboard data. Some information may not be current.");
            }
        }

        private async Task LoadTotalItemsAsync()
        {
            try
            {
                var items = await _inventoryService.GetAllItemsAsync();
                TotalItems = items?.Count() ?? 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading total items count");
                TotalItems = 0;
            }
        }

        private async Task LoadLowStockItemsAsync()
        {
            try
            {
                var lowStockItems = await _inventoryService.GetLowStockItemsAsync();
                LowStockItems = lowStockItems?.Count() ?? 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading low stock items count");
                LowStockItems = 0;
            }
        }

        private async Task LoadTodaySalesAsync()
        {
            try
            {
                var today = DateTime.Today;
                var sales = await _salesService.GetSalesByDateRangeAsync(today, today.AddDays(1));
                TodaySales = sales?.Sum(s => s.Total) ?? 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading today's sales");
                TodaySales = 0;
            }
        }

        private async Task LoadTotalCustomersAsync()
        {
            try
            {
                var customers = await _customerService.GetAllCustomersAsync();
                TotalCustomers = customers?.Count() ?? 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading total customers count");
                TotalCustomers = 0;
            }
        }

        #endregion

        #region Helper Methods

        private void ShowErrorMessage(string message)
        {
            _messageService?.ShowError(message) ??
                System.Windows.MessageBox.Show(message, "Error",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }
}
