using InventoryManagement.Data;
using InventoryManagement.Models;
using InventoryManagement.Models.Reports;
using InventoryManagement.Services.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Offline implementation of sales service for pure offline operation
    /// </summary>
    public class OfflineSalesService : Data.ISalesService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<OfflineSalesService> _logger;

        public OfflineSalesService(ApplicationDbContext dbContext, ILogger<OfflineSalesService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<List<SalesTransaction>> GetSalesForPeriodAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                _logger.LogInformation("Getting sales for period {StartDate} to {EndDate}", startDate, endDate);

                var sales = await _dbContext.Sales
                    .Include(s => s.User)
                    .Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate)
                    .Select(s => new SalesTransaction
                    {
                        Id = s.Id,
                        UserId = s.UserId,
                        UserName = s.User != null ? s.User.FullName : "Unknown",
                        LocationId = s.LocationId ?? 1,
                        LocationName = "Main Location", // Default location
                        TransactionDate = s.SaleDate,
                        TotalAmount = s.TotalAmount,
                        PaymentMethod = s.PaymentMethod.ToString(),
                        TransactionStatus = "Completed"
                    })
                    .ToListAsync();

                _logger.LogInformation("Found {Count} sales transactions", sales.Count);
                return sales;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sales for period");
                return new List<SalesTransaction>();
            }
        }

        public async Task<List<SalesDetail>> GetDetailedSalesForPeriodAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                _logger.LogInformation("Getting detailed sales for period {StartDate} to {EndDate}", startDate, endDate);

                var salesDetails = await _dbContext.Sales
                    .Include(s => s.SaleItems)
                    .ThenInclude(si => si.Product)
                    .Include(s => s.User)
                    .Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate)
                    .SelectMany(s => s.SaleItems.Select(si => new SalesDetail
                    {
                        SaleId = s.Id,
                        SaleDate = s.SaleDate,
                        UserId = s.UserId,
                        UserName = s.User != null ? s.User.FullName : "Unknown",
                        ProductId = si.ProductId,
                        ProductName = si.Product != null ? si.Product.Name : "Unknown Product",
                        ProductCode = si.Product != null ? si.Product.Code : "",
                        Quantity = si.Quantity,
                        UnitPrice = si.UnitPrice,
                        TotalPrice = si.TotalPrice,
                        PaymentMethod = s.PaymentMethod.ToString()
                    }))
                    .ToListAsync();

                _logger.LogInformation("Found {Count} detailed sales records", salesDetails.Count);
                return salesDetails;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting detailed sales for period");
                return new List<SalesDetail>();
            }
        }

        public async Task<List<SalesTransaction>> GetSalesByCashierAsync(int cashierId, DateTime startDate, DateTime endDate)
        {
            try
            {
                _logger.LogInformation("Getting sales for cashier {CashierId} from {StartDate} to {EndDate}", 
                    cashierId, startDate, endDate);

                var sales = await _dbContext.Sales
                    .Include(s => s.User)
                    .Where(s => s.UserId == cashierId && s.SaleDate >= startDate && s.SaleDate <= endDate)
                    .Select(s => new SalesTransaction
                    {
                        Id = s.Id,
                        UserId = s.UserId,
                        UserName = s.User != null ? s.User.FullName : "Unknown",
                        LocationId = s.LocationId ?? 1,
                        LocationName = "Main Location",
                        TransactionDate = s.SaleDate,
                        TotalAmount = s.TotalAmount,
                        PaymentMethod = s.PaymentMethod.ToString(),
                        TransactionStatus = "Completed"
                    })
                    .ToListAsync();

                _logger.LogInformation("Found {Count} sales for cashier {CashierId}", sales.Count, cashierId);
                return sales;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sales for cashier {CashierId}", cashierId);
                return new List<SalesTransaction>();
            }
        }

        public async Task<List<SalesTransaction>> GetSalesByLocationAsync(int locationId, DateTime startDate, DateTime endDate)
        {
            try
            {
                _logger.LogInformation("Getting sales for location {LocationId} from {StartDate} to {EndDate}", 
                    locationId, startDate, endDate);

                var sales = await _dbContext.Sales
                    .Include(s => s.User)
                    .Where(s => (s.LocationId ?? 1) == locationId && s.SaleDate >= startDate && s.SaleDate <= endDate)
                    .Select(s => new SalesTransaction
                    {
                        Id = s.Id,
                        UserId = s.UserId,
                        UserName = s.User != null ? s.User.FullName : "Unknown",
                        LocationId = s.LocationId ?? 1,
                        LocationName = "Main Location",
                        TransactionDate = s.SaleDate,
                        TotalAmount = s.TotalAmount,
                        PaymentMethod = s.PaymentMethod.ToString(),
                        TransactionStatus = "Completed"
                    })
                    .ToListAsync();

                _logger.LogInformation("Found {Count} sales for location {LocationId}", sales.Count, locationId);
                return sales;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sales for location {LocationId}", locationId);
                return new List<SalesTransaction>();
            }
        }
    }
}
