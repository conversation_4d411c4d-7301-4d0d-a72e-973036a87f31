using System;
using System.Threading;
using System.Windows;

namespace InventoryManagement.Views
{
    /// <summary>
    /// Progress window for database setup operations
    /// Provides user-friendly feedback during PostgreSQL initialization
    /// </summary>
    public partial class DatabaseSetupProgressWindow : Window
    {
        private CancellationTokenSource _cancellationTokenSource;
        private bool _canCancel = true;

        public DatabaseSetupProgressWindow()
        {
            InitializeComponent();
            _cancellationTokenSource = new CancellationTokenSource();
        }

        /// <summary>
        /// Updates the progress bar and status text
        /// </summary>
        /// <param name="statusText">Status message to display</param>
        /// <param name="progressPercentage">Progress percentage (0-100)</param>
        public void UpdateProgress(string statusText, int progressPercentage)
        {
            // Ensure UI updates happen on the UI thread
            Dispatcher.Invoke(() =>
            {
                StatusTextBlock.Text = statusText;
                SetupProgressBar.Value = progressPercentage;
                PercentageTextBlock.Text = $"{progressPercentage}%";

                // Hide cancel button when near completion
                if (progressPercentage >= 90)
                {
                    CancelButton.Visibility = Visibility.Collapsed;
                    _canCancel = false;
                }
                else if (_canCancel)
                {
                    CancelButton.Visibility = Visibility.Visible;
                }
            });
        }

        /// <summary>
        /// Shows an error message and allows user to close the window
        /// </summary>
        /// <param name="errorMessage">Error message to display</param>
        public void ShowError(string errorMessage)
        {
            Dispatcher.Invoke(() =>
            {
                StatusTextBlock.Text = $"Error: {errorMessage}";
                SetupProgressBar.Foreground = System.Windows.Media.Brushes.Red;
                CancelButton.Content = "Close";
                CancelButton.Visibility = Visibility.Visible;
                _canCancel = false;
            });
        }

        /// <summary>
        /// Shows success message
        /// </summary>
        /// <param name="successMessage">Success message to display</param>
        public void ShowSuccess(string successMessage)
        {
            Dispatcher.Invoke(() =>
            {
                StatusTextBlock.Text = successMessage;
                SetupProgressBar.Value = 100;
                PercentageTextBlock.Text = "100%";
                SetupProgressBar.Foreground = System.Windows.Media.Brushes.Green;
                CancelButton.Visibility = Visibility.Collapsed;
            });
        }

        /// <summary>
        /// Enables or disables the cancel functionality
        /// </summary>
        /// <param name="canCancel">Whether cancellation is allowed</param>
        public void SetCancelable(bool canCancel)
        {
            _canCancel = canCancel;
            Dispatcher.Invoke(() =>
            {
                CancelButton.Visibility = canCancel ? Visibility.Visible : Visibility.Collapsed;
            });
        }

        /// <summary>
        /// Gets the cancellation token for the setup operation
        /// </summary>
        public CancellationToken CancellationToken => _cancellationTokenSource.Token;

        /// <summary>
        /// Handles the cancel button click
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            if (_canCancel)
            {
                var result = MessageBox.Show(
                    "Are you sure you want to cancel the database setup?\n\n" +
                    "The application may not work properly without a properly configured database.",
                    "Cancel Setup",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    _cancellationTokenSource.Cancel();
                    Close();
                }
            }
            else
            {
                // Close button when setup is complete or failed
                Close();
            }
        }

        /// <summary>
        /// Handles window closing
        /// </summary>
        protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
        {
            if (_canCancel && !_cancellationTokenSource.Token.IsCancellationRequested)
            {
                var result = MessageBox.Show(
                    "Database setup is still in progress.\n\n" +
                    "Are you sure you want to close this window?",
                    "Setup In Progress",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.No)
                {
                    e.Cancel = true;
                    return;
                }

                _cancellationTokenSource.Cancel();
            }

            base.OnClosing(e);
        }

        /// <summary>
        /// Cleanup resources
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            _cancellationTokenSource?.Dispose();
            base.OnClosed(e);
        }
    }
}
