using InventoryManagement.ViewModels;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Windows;

namespace InventoryManagement.Views
{
    /// <summary>
    /// Interaction logic for SimplifiedDashboard.xaml
    /// User-friendly main dashboard for easy navigation and operation
    /// </summary>
    public partial class SimplifiedDashboard : Window
    {
        private readonly ILogger<SimplifiedDashboard> _logger;
        private readonly SimplifiedDashboardViewModel _viewModel;

        public SimplifiedDashboard()
        {
            InitializeComponent();
            
            try
            {
                // Get services from DI container
                var serviceProvider = ((App)Application.Current).ServiceProvider;
                _logger = serviceProvider.GetRequiredService<ILogger<SimplifiedDashboard>>();
                _viewModel = serviceProvider.GetRequiredService<SimplifiedDashboardViewModel>();
                
                // Set DataContext
                DataContext = _viewModel;
                
                _logger.LogInformation("Simplified Dashboard initialized successfully");
            }
            catch (Exception ex)
            {
                // Fallback error handling if D<PERSON> fails
                MessageBox.Show($"Error initializing dashboard: {ex.Message}", 
                    "Initialization Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Handle window loaded event
        /// </summary>
        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                _logger?.LogInformation("Simplified Dashboard window loaded");
                
                // Set focus to the main content
                Focus();
                
                // Show welcome message for first-time users
                ShowWelcomeMessageIfNeeded();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error during window load");
            }
        }

        /// <summary>
        /// Handle window closing event
        /// </summary>
        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                _logger?.LogInformation("Simplified Dashboard closing");
                
                // Ask for confirmation before closing
                var result = MessageBox.Show(
                    "Are you sure you want to close the application?", 
                    "Confirm Exit", 
                    MessageBoxButton.YesNo, 
                    MessageBoxImage.Question);
                
                if (result == MessageBoxResult.No)
                {
                    e.Cancel = true;
                    return;
                }
                
                // Close all other windows
                CloseAllOtherWindows();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error during window closing");
            }
        }

        /// <summary>
        /// Show welcome message for new users
        /// </summary>
        private void ShowWelcomeMessageIfNeeded()
        {
            try
            {
                // Check if this is the first time running the application
                var settings = Properties.Settings.Default;
                
                if (!settings.HasShownWelcome)
                {
                    var welcomeMessage = @"Welcome to Tom General Trading Inventory Management System!

This system is designed to work completely offline and be easy to use.

Quick Start Guide:
• Use 'Point of Sale' to process customer transactions
• Use 'Manage Items' to add and update your inventory
• Use 'Customers' to manage customer information
• Use 'Reports' to view business analytics
• Use 'Settings' to configure hardware and system options

The system automatically saves all your data locally and creates regular backups.

Click 'Help' anytime for detailed instructions.

Would you like to see the setup wizard to configure your hardware?";

                    var result = MessageBox.Show(welcomeMessage, 
                        "Welcome to Tom General Trading", 
                        MessageBoxButton.YesNo, 
                        MessageBoxImage.Information);
                    
                    if (result == MessageBoxResult.Yes)
                    {
                        // Open setup wizard
                        ShowSetupWizard();
                    }
                    
                    // Mark welcome as shown
                    settings.HasShownWelcome = true;
                    settings.Save();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error showing welcome message");
            }
        }

        /// <summary>
        /// Show the setup wizard for new users
        /// </summary>
        private void ShowSetupWizard()
        {
            try
            {
                _logger?.LogInformation("Opening setup wizard");
                
                var setupWizard = new SetupWizardWindow();
                setupWizard.Owner = this;
                setupWizard.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error opening setup wizard");
                MessageBox.Show("Unable to open setup wizard. You can access hardware settings from the Settings menu.", 
                    "Setup Wizard", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// Close all other application windows
        /// </summary>
        private void CloseAllOtherWindows()
        {
            try
            {
                var windows = Application.Current.Windows;
                for (int i = windows.Count - 1; i >= 0; i--)
                {
                    var window = windows[i];
                    if (window != this && window.GetType() != typeof(Views.LoginWindow))
                    {
                        window.Close();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error closing other windows");
            }
        }

        /// <summary>
        /// Handle keyboard shortcuts
        /// </summary>
        private void Window_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            try
            {
                // Handle common keyboard shortcuts
                if (e.Key == System.Windows.Input.Key.F1)
                {
                    // F1 - Help
                    _viewModel?.OpenHelpCommand?.Execute(null);
                    e.Handled = true;
                }
                else if (e.Key == System.Windows.Input.Key.F2)
                {
                    // F2 - Quick Sale
                    _viewModel?.QuickSaleCommand?.Execute(null);
                    e.Handled = true;
                }
                else if (e.Key == System.Windows.Input.Key.F3)
                {
                    // F3 - Add Item
                    _viewModel?.AddItemCommand?.Execute(null);
                    e.Handled = true;
                }
                else if (e.Key == System.Windows.Input.Key.F4)
                {
                    // F4 - Stock Check
                    _viewModel?.StockCheckCommand?.Execute(null);
                    e.Handled = true;
                }
                else if (e.Key == System.Windows.Input.Key.F5)
                {
                    // F5 - Refresh Dashboard
                    RefreshDashboard();
                    e.Handled = true;
                }
                else if (e.Key == System.Windows.Input.Key.Escape)
                {
                    // Escape - Close current dialog or minimize
                    WindowState = WindowState.Minimized;
                    e.Handled = true;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error handling keyboard shortcut");
            }
        }

        /// <summary>
        /// Refresh the dashboard data
        /// </summary>
        private void RefreshDashboard()
        {
            try
            {
                _logger?.LogInformation("Refreshing dashboard data");
                
                // Reload the view model data
                if (_viewModel != null)
                {
                    // Trigger data reload
                    var loadMethod = _viewModel.GetType().GetMethod("LoadDashboardDataAsync", 
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    loadMethod?.Invoke(_viewModel, null);
                }
                
                // Show refresh confirmation
                ShowStatusMessage("Dashboard refreshed successfully");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error refreshing dashboard");
                ShowStatusMessage("Error refreshing dashboard");
            }
        }

        /// <summary>
        /// Show a temporary status message
        /// </summary>
        private void ShowStatusMessage(string message)
        {
            try
            {
                // This could be enhanced with a status bar or notification system
                // For now, we'll just log it
                _logger?.LogInformation("Status: {Message}", message);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error showing status message");
            }
        }
    }
}
