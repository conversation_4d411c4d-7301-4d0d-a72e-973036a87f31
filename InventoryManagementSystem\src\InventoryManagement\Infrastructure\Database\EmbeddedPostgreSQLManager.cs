using Microsoft.Extensions.Logging;
using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Threading.Tasks;
using System.Threading;
using Npgsql;

namespace InventoryManagement.Infrastructure.Database
{
    /// <summary>
    /// Manages embedded PostgreSQL instance for offline operation
    /// Provides user-friendly PostgreSQL setup without requiring separate installation
    /// </summary>
    public class EmbeddedPostgreSQLManager : IDisposable
    {
        private readonly ILogger<EmbeddedPostgreSQLManager> _logger;
        private readonly string _applicationPath;
        private readonly string _postgresPath;
        private readonly string _dataPath;
        private readonly int _port;
        private Process _postgresProcess;
        private bool _disposed;

        public EmbeddedPostgreSQLManager(ILogger<EmbeddedPostgreSQLManager> logger, int port = 5432)
        {
            _logger = logger;
            _port = port;
            _applicationPath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) ?? "";
            _postgresPath = Path.Combine(_applicationPath, "PostgreSQL");
            _dataPath = Path.Combine(_postgresPath, "data");
        }

        /// <summary>
        /// Checks if PostgreSQL binaries are available
        /// </summary>
        public bool IsPostgreSQLAvailable()
        {
            var postgresExe = Path.Combine(_postgresPath, "bin", "postgres.exe");
            return File.Exists(postgresExe);
        }

        /// <summary>
        /// Checks if database is initialized
        /// </summary>
        public bool IsDatabaseInitialized()
        {
            var pgVersionFile = Path.Combine(_dataPath, "PG_VERSION");
            return File.Exists(pgVersionFile);
        }

        /// <summary>
        /// Initializes PostgreSQL database cluster for first-time use
        /// </summary>
        public async Task<bool> InitializeDatabaseAsync()
        {
            try
            {
                _logger.LogInformation("Initializing PostgreSQL database cluster...");

                if (!IsPostgreSQLAvailable())
                {
                    _logger.LogError("PostgreSQL binaries not found at {Path}", _postgresPath);
                    return false;
                }

                if (IsDatabaseInitialized())
                {
                    _logger.LogInformation("Database already initialized");
                    return true;
                }

                // Create data directory
                Directory.CreateDirectory(_dataPath);

                // Run initdb
                var initdbPath = Path.Combine(_postgresPath, "bin", "initdb.exe");
                var startInfo = new ProcessStartInfo
                {
                    FileName = initdbPath,
                    Arguments = $"-D \"{_dataPath}\" -U postgres --auth-local=trust --encoding=UTF8",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var process = Process.Start(startInfo);
                if (process == null)
                {
                    _logger.LogError("Failed to start initdb process");
                    return false;
                }

                await process.WaitForExitAsync();

                if (process.ExitCode == 0)
                {
                    _logger.LogInformation("Database initialization completed successfully");
                    return true;
                }
                else
                {
                    var error = await process.StandardError.ReadToEndAsync();
                    _logger.LogError("Database initialization failed: {Error}", error);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing database");
                return false;
            }
        }

        /// <summary>
        /// Starts embedded PostgreSQL server
        /// </summary>
        public async Task<bool> StartPostgreSQLAsync()
        {
            try
            {
                if (_postgresProcess != null && !_postgresProcess.HasExited)
                {
                    _logger.LogInformation("PostgreSQL is already running");
                    return true;
                }

                _logger.LogInformation("Starting embedded PostgreSQL server...");

                var postgresExe = Path.Combine(_postgresPath, "bin", "postgres.exe");
                var startInfo = new ProcessStartInfo
                {
                    FileName = postgresExe,
                    Arguments = $"-D \"{_dataPath}\" -p {_port}",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                _postgresProcess = Process.Start(startInfo);
                if (_postgresProcess == null)
                {
                    _logger.LogError("Failed to start PostgreSQL process");
                    return false;
                }

                // Wait for PostgreSQL to be ready
                return await WaitForDatabaseReadyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting PostgreSQL");
                return false;
            }
        }

        /// <summary>
        /// Waits for PostgreSQL to be ready to accept connections
        /// </summary>
        private async Task<bool> WaitForDatabaseReadyAsync(int timeoutSeconds = 30)
        {
            var connectionString = $"Host=localhost;Port={_port};Database=postgres;Username=postgres;";
            var timeout = DateTime.Now.AddSeconds(timeoutSeconds);

            while (DateTime.Now < timeout)
            {
                try
                {
                    using var connection = new NpgsqlConnection(connectionString);
                    await connection.OpenAsync();
                    await connection.CloseAsync();
                    
                    _logger.LogInformation("PostgreSQL is ready to accept connections");
                    return true;
                }
                catch
                {
                    // PostgreSQL not ready yet, wait and retry
                    await Task.Delay(1000);
                }
            }

            _logger.LogError("Timeout waiting for PostgreSQL to be ready");
            return false;
        }

        /// <summary>
        /// Stops PostgreSQL server gracefully
        /// </summary>
        public async Task StopPostgreSQLAsync()
        {
            try
            {
                if (_postgresProcess == null || _postgresProcess.HasExited)
                {
                    _logger.LogInformation("PostgreSQL is not running");
                    return;
                }

                _logger.LogInformation("Stopping PostgreSQL server...");

                // Try graceful shutdown first
                var pgCtlPath = Path.Combine(_postgresPath, "bin", "pg_ctl.exe");
                var stopInfo = new ProcessStartInfo
                {
                    FileName = pgCtlPath,
                    Arguments = $"stop -D \"{_dataPath}\" -m fast",
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var stopProcess = Process.Start(stopInfo);
                if (stopProcess != null)
                {
                    await stopProcess.WaitForExitAsync();
                }

                // Wait for process to exit
                if (!_postgresProcess.WaitForExit(10000))
                {
                    _logger.LogWarning("PostgreSQL did not stop gracefully, forcing termination");
                    _postgresProcess.Kill();
                }

                _postgresProcess.Dispose();
                _postgresProcess = null;
                
                _logger.LogInformation("PostgreSQL stopped successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error stopping PostgreSQL");
            }
        }

        /// <summary>
        /// Gets connection string for the embedded PostgreSQL instance
        /// </summary>
        public string GetConnectionString(string databaseName = "TomGeneralTradingDB")
        {
            return $"Host=localhost;Port={_port};Database={databaseName};Username=postgres;Pooling=true;Minimum Pool Size=5;Maximum Pool Size=100;Connection Lifetime=300";
        }

        /// <summary>
        /// Checks if PostgreSQL process is running
        /// </summary>
        public bool IsRunning()
        {
            return _postgresProcess != null && !_postgresProcess.HasExited;
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                StopPostgreSQLAsync().Wait();
                _disposed = true;
            }
        }
    }
}
