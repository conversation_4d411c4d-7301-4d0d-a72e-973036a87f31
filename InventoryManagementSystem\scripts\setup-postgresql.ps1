# PostgreSQL Setup Script for Tom General Trading Inventory System
# This script helps users set up PostgreSQL for offline operation

param(
    [switch]$Portable,
    [switch]$Service,
    [string]$InstallPath = ".\PostgreSQL"
)

Write-Host "=== Tom General Trading - PostgreSQL Setup ===" -ForegroundColor Green
Write-Host ""

# Check if running as administrator for service installation
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Download PostgreSQL portable version
function Download-PostgreSQLPortable {
    param([string]$DestinationPath)
    
    Write-Host "Downloading PostgreSQL portable version..." -ForegroundColor Yellow
    
    # PostgreSQL 15 portable download URL (you would need to update this with actual URL)
    $downloadUrl = "https://get.enterprisedb.com/postgresql/postgresql-15.4-1-windows-x64-binaries.zip"
    $zipFile = Join-Path $env:TEMP "postgresql-portable.zip"
    
    try {
        # Create destination directory
        if (!(Test-Path $DestinationPath)) {
            New-Item -ItemType Directory -Path $DestinationPath -Force | Out-Null
        }
        
        Write-Host "Note: Automatic download is not yet implemented." -ForegroundColor Red
        Write-Host "Please manually download PostgreSQL portable from:" -ForegroundColor Yellow
        Write-Host $downloadUrl -ForegroundColor Cyan
        Write-Host "Extract to: $DestinationPath" -ForegroundColor Yellow
        
        # Open download page
        $openBrowser = Read-Host "Open download page in browser? (y/n)"
        if ($openBrowser -eq 'y' -or $openBrowser -eq 'Y') {
            Start-Process "https://www.enterprisedb.com/download-postgresql-binaries"
        }
        
        return $false
    }
    catch {
        Write-Host "Error downloading PostgreSQL: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Initialize PostgreSQL database
function Initialize-PostgreSQLDatabase {
    param([string]$PostgreSQLPath)
    
    $initdbPath = Join-Path $PostgreSQLPath "bin\initdb.exe"
    $dataPath = Join-Path $PostgreSQLPath "data"
    
    if (!(Test-Path $initdbPath)) {
        Write-Host "PostgreSQL binaries not found at $PostgreSQLPath" -ForegroundColor Red
        return $false
    }
    
    if (Test-Path $dataPath) {
        Write-Host "Database already initialized at $dataPath" -ForegroundColor Green
        return $true
    }
    
    Write-Host "Initializing PostgreSQL database..." -ForegroundColor Yellow
    
    try {
        & $initdbPath -D $dataPath -U postgres --auth-local=trust --encoding=UTF8
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Database initialized successfully!" -ForegroundColor Green
            return $true
        } else {
            Write-Host "Database initialization failed with exit code $LASTEXITCODE" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "Error initializing database: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Create application database
function Create-ApplicationDatabase {
    param([string]$PostgreSQLPath)
    
    $psqlPath = Join-Path $PostgreSQLPath "bin\psql.exe"
    
    if (!(Test-Path $psqlPath)) {
        Write-Host "psql.exe not found at $PostgreSQLPath" -ForegroundColor Red
        return $false
    }
    
    Write-Host "Creating application database..." -ForegroundColor Yellow
    
    try {
        # Create database
        $createDbCommand = "CREATE DATABASE `"TomGeneralTradingDB`" WITH ENCODING 'UTF8';"
        & $psqlPath -U postgres -d postgres -c $createDbCommand
        
        Write-Host "Application database created successfully!" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "Error creating application database: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Start PostgreSQL server
function Start-PostgreSQLServer {
    param([string]$PostgreSQLPath)
    
    $postgresPath = Join-Path $PostgreSQLPath "bin\postgres.exe"
    $dataPath = Join-Path $PostgreSQLPath "data"
    
    if (!(Test-Path $postgresPath)) {
        Write-Host "postgres.exe not found at $PostgreSQLPath" -ForegroundColor Red
        return $false
    }
    
    Write-Host "Starting PostgreSQL server..." -ForegroundColor Yellow
    
    try {
        # Start PostgreSQL in background
        Start-Process -FilePath $postgresPath -ArgumentList "-D `"$dataPath`" -p 5432" -WindowStyle Hidden
        
        # Wait a moment for server to start
        Start-Sleep -Seconds 3
        
        Write-Host "PostgreSQL server started!" -ForegroundColor Green
        Write-Host "Server is running on localhost:5432" -ForegroundColor Cyan
        return $true
    }
    catch {
        Write-Host "Error starting PostgreSQL server: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Main setup logic
Write-Host "Choose setup option:" -ForegroundColor Cyan
Write-Host "1. Portable installation (recommended for single user)" -ForegroundColor White
Write-Host "2. System service installation (requires administrator)" -ForegroundColor White
Write-Host "3. Check existing installation" -ForegroundColor White

$choice = Read-Host "Enter choice (1-3)"

switch ($choice) {
    "1" {
        Write-Host "Setting up portable PostgreSQL installation..." -ForegroundColor Green
        
        $fullInstallPath = Resolve-Path $InstallPath -ErrorAction SilentlyContinue
        if (!$fullInstallPath) {
            $fullInstallPath = Join-Path (Get-Location) $InstallPath
        }
        
        Write-Host "Installation path: $fullInstallPath" -ForegroundColor Cyan
        
        # Check if PostgreSQL binaries exist
        $postgresExe = Join-Path $fullInstallPath "bin\postgres.exe"
        if (!(Test-Path $postgresExe)) {
            Write-Host "PostgreSQL binaries not found. Downloading..." -ForegroundColor Yellow
            if (!(Download-PostgreSQLPortable -DestinationPath $fullInstallPath)) {
                Write-Host "Setup failed. Please download PostgreSQL manually." -ForegroundColor Red
                exit 1
            }
        }
        
        # Initialize database
        if (Initialize-PostgreSQLDatabase -PostgreSQLPath $fullInstallPath) {
            # Start server
            if (Start-PostgreSQLServer -PostgreSQLPath $fullInstallPath) {
                # Create application database
                Start-Sleep -Seconds 2
                Create-ApplicationDatabase -PostgreSQLPath $fullInstallPath
                
                Write-Host ""
                Write-Host "=== Setup Complete! ===" -ForegroundColor Green
                Write-Host "PostgreSQL is now running and ready for the application." -ForegroundColor White
                Write-Host "You can now start the Tom General Trading application." -ForegroundColor White
            }
        }
    }
    
    "2" {
        if (!(Test-Administrator)) {
            Write-Host "Administrator privileges required for service installation." -ForegroundColor Red
            Write-Host "Please run PowerShell as Administrator and try again." -ForegroundColor Yellow
            exit 1
        }
        
        Write-Host "System service installation not yet implemented." -ForegroundColor Yellow
        Write-Host "Please use portable installation (option 1) for now." -ForegroundColor White
    }
    
    "3" {
        Write-Host "Checking for existing PostgreSQL installation..." -ForegroundColor Yellow
        
        # Check for system PostgreSQL
        $systemPostgres = Get-Service -Name "postgresql*" -ErrorAction SilentlyContinue
        if ($systemPostgres) {
            Write-Host "System PostgreSQL service found: $($systemPostgres.Name)" -ForegroundColor Green
            Write-Host "Status: $($systemPostgres.Status)" -ForegroundColor Cyan
        }
        
        # Check for portable PostgreSQL
        $portablePostgres = Join-Path $InstallPath "bin\postgres.exe"
        if (Test-Path $portablePostgres) {
            Write-Host "Portable PostgreSQL found at: $InstallPath" -ForegroundColor Green
        }
        
        if (!$systemPostgres -and !(Test-Path $portablePostgres)) {
            Write-Host "No PostgreSQL installation found." -ForegroundColor Red
            Write-Host "Please run setup option 1 or 2 to install PostgreSQL." -ForegroundColor Yellow
        }
    }
    
    default {
        Write-Host "Invalid choice. Please run the script again." -ForegroundColor Red
        exit 1
    }
}

Write-Host ""
Write-Host "Setup script completed." -ForegroundColor Green
