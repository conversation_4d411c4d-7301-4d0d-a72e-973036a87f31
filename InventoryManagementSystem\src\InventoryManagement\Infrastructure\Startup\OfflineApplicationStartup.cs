using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Threading.Tasks;
using System.Windows;
using InventoryManagement.Infrastructure.Database;
using InventoryManagement.Infrastructure.Configuration;
using InventoryManagement.DataAccess;
using Microsoft.EntityFrameworkCore;

namespace InventoryManagement.Infrastructure.Startup
{
    /// <summary>
    /// Handles application startup for offline PostgreSQL-only operation
    /// Ensures PostgreSQL is running and database is properly initialized
    /// </summary>
    public class OfflineApplicationStartup
    {
        private readonly ILogger<OfflineApplicationStartup> _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly AppConfiguration _configuration;

        public OfflineApplicationStartup(
            ILogger<OfflineApplicationStartup> logger,
            IServiceProvider serviceProvider,
            AppConfiguration configuration)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
            _configuration = configuration;
        }

        /// <summary>
        /// Initializes the application for offline operation
        /// </summary>
        public async Task<bool> InitializeAsync()
        {
            try
            {
                _logger.LogInformation("Starting offline application initialization...");

                // Step 1: Initialize embedded PostgreSQL
                if (!await InitializePostgreSQLAsync())
                {
                    return false;
                }

                // Step 2: Ensure database schema is up to date
                if (!await EnsureDatabaseSchemaAsync())
                {
                    return false;
                }

                // Step 3: Verify essential data exists
                if (!await VerifyEssentialDataAsync())
                {
                    return false;
                }

                _logger.LogInformation("Offline application initialization completed successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize offline application");
                
                MessageBox.Show(
                    $"Application initialization failed: {ex.Message}\n\n" +
                    "Please check the logs for more details.",
                    "Initialization Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);

                return false;
            }
        }

        /// <summary>
        /// Initializes PostgreSQL for offline operation
        /// </summary>
        private async Task<bool> InitializePostgreSQLAsync()
        {
            try
            {
                var postgresManager = _serviceProvider.GetRequiredService<EmbeddedPostgreSQLManager>();

                // Check if this is first run
                bool isFirstRun = !postgresManager.IsDatabaseInitialized();

                if (isFirstRun)
                {
                    _logger.LogInformation("First run detected, starting database setup wizard...");
                    
                    var setupWizard = _serviceProvider.GetRequiredService<DatabaseSetupWizard>();
                    return await setupWizard.RunFirstTimeSetupAsync();
                }
                else
                {
                    // Existing installation, just start PostgreSQL
                    _logger.LogInformation("Existing installation detected, starting PostgreSQL...");
                    
                    if (!postgresManager.IsRunning())
                    {
                        if (!await postgresManager.StartPostgreSQLAsync())
                        {
                            throw new Exception("Failed to start PostgreSQL server");
                        }
                    }

                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing PostgreSQL");
                return false;
            }
        }

        /// <summary>
        /// Ensures database schema is up to date
        /// </summary>
        private async Task<bool> EnsureDatabaseSchemaAsync()
        {
            try
            {
                _logger.LogInformation("Checking database schema...");

                using var scope = _serviceProvider.CreateScope();
                var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

                // Check if database exists and is accessible
                if (!await dbContext.Database.CanConnectAsync())
                {
                    throw new Exception("Cannot connect to database");
                }

                // Apply any pending migrations
                var pendingMigrations = await dbContext.Database.GetPendingMigrationsAsync();
                if (pendingMigrations.Any())
                {
                    _logger.LogInformation("Applying database migrations...");
                    await dbContext.Database.MigrateAsync();
                    _logger.LogInformation("Database migrations applied successfully");
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error ensuring database schema");
                return false;
            }
        }

        /// <summary>
        /// Verifies that essential data exists in the database
        /// </summary>
        private async Task<bool> VerifyEssentialDataAsync()
        {
            try
            {
                _logger.LogInformation("Verifying essential data...");

                using var scope = _serviceProvider.CreateScope();
                var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

                // Check if admin user exists
                var adminExists = await dbContext.Users.AnyAsync(u => u.Role == Models.UserRole.Admin);
                if (!adminExists)
                {
                    _logger.LogWarning("No admin user found, creating default admin user...");
                    await CreateDefaultAdminUserAsync(dbContext);
                }

                // Check if basic categories exist
                var categoriesExist = await dbContext.Categories.AnyAsync();
                if (!categoriesExist)
                {
                    _logger.LogInformation("No categories found, creating default categories...");
                    await CreateDefaultCategoriesAsync(dbContext);
                }

                // Check if basic locations exist
                var locationsExist = await dbContext.Locations.AnyAsync();
                if (!locationsExist)
                {
                    _logger.LogInformation("No locations found, creating default locations...");
                    await CreateDefaultLocationsAsync(dbContext);
                }

                await dbContext.SaveChangesAsync();
                _logger.LogInformation("Essential data verification completed");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying essential data");
                return false;
            }
        }

        /// <summary>
        /// Creates default admin user if none exists
        /// </summary>
        private async Task CreateDefaultAdminUserAsync(ApplicationDbContext dbContext)
        {
            var adminUser = new Models.User
            {
                Username = "admin",
                PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"),
                FullName = "System Administrator",
                Email = "<EMAIL>",
                Role = Models.UserRole.Admin,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            dbContext.Users.Add(adminUser);
            _logger.LogInformation("Default admin user created");
        }

        /// <summary>
        /// Creates default categories
        /// </summary>
        private async Task CreateDefaultCategoriesAsync(ApplicationDbContext dbContext)
        {
            var categories = new[]
            {
                new Models.Category { Name = "Electronics", Description = "Electronic items and accessories" },
                new Models.Category { Name = "Clothing", Description = "Clothing and apparel" },
                new Models.Category { Name = "Food & Beverages", Description = "Food and drink items" },
                new Models.Category { Name = "Home & Garden", Description = "Home and garden supplies" },
                new Models.Category { Name = "Books & Media", Description = "Books, movies, and media" }
            };

            dbContext.Categories.AddRange(categories);
            _logger.LogInformation("Default categories created");
        }

        /// <summary>
        /// Creates default locations
        /// </summary>
        private async Task CreateDefaultLocationsAsync(ApplicationDbContext dbContext)
        {
            var locations = new[]
            {
                new Models.Location { Name = "Main Store", Description = "Main retail floor" },
                new Models.Location { Name = "Basement Storage", Description = "Basement storage area" },
                new Models.Location { Name = "Back Office", Description = "Office storage area" }
            };

            dbContext.Locations.AddRange(locations);
            _logger.LogInformation("Default locations created");
        }

        /// <summary>
        /// Performs cleanup when application shuts down
        /// </summary>
        public async Task ShutdownAsync()
        {
            try
            {
                _logger.LogInformation("Shutting down application...");

                var postgresManager = _serviceProvider.GetRequiredService<EmbeddedPostgreSQLManager>();
                await postgresManager.StopPostgreSQLAsync();

                _logger.LogInformation("Application shutdown completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during application shutdown");
            }
        }
    }
}
