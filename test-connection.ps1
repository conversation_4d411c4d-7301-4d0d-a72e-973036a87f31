# Test PostgreSQL Connection Script
# This script tests if your application can connect to PostgreSQL

Write-Host "=== Testing PostgreSQL Connection ===" -ForegroundColor Green
Write-Host ""

# Connection parameters
$server = "localhost"
$port = "5432"
$database = "TomGeneralTradingDB"
$username = "postgres"
$password = "abab"

Write-Host "Testing connection to:" -ForegroundColor Yellow
Write-Host "Server: $server" -ForegroundColor Cyan
Write-Host "Port: $port" -ForegroundColor Cyan
Write-Host "Database: $database" -ForegroundColor Cyan
Write-Host "Username: $username" -ForegroundColor Cyan
Write-Host ""

try {
    # Test using .NET connection (similar to your application)
    Add-Type -AssemblyName "System.Data"
    
    $connectionString = "Host=$server;Port=$port;Database=$database;Username=$username;Password=$password;"
    
    Write-Host "Connection String: $connectionString" -ForegroundColor Gray
    Write-Host ""
    
    # Try to load Npgsql if available
    try {
        Add-Type -Path "Npgsql.dll" -ErrorAction SilentlyContinue
        Write-Host "Using Npgsql driver..." -ForegroundColor Yellow
    } catch {
        Write-Host "Npgsql not found, using basic test..." -ForegroundColor Yellow
    }
    
    # Test basic connectivity
    Write-Host "Testing basic connectivity..." -ForegroundColor Yellow
    
    $tcpClient = New-Object System.Net.Sockets.TcpClient
    $tcpClient.Connect($server, $port)
    
    if ($tcpClient.Connected) {
        Write-Host "✅ TCP Connection successful!" -ForegroundColor Green
        $tcpClient.Close()
    } else {
        Write-Host "❌ TCP Connection failed!" -ForegroundColor Red
        exit 1
    }
    
    Write-Host ""
    Write-Host "=== Connection Test Results ===" -ForegroundColor Green
    Write-Host "✅ PostgreSQL server is reachable" -ForegroundColor Green
    Write-Host "✅ Port 5432 is accessible" -ForegroundColor Green
    Write-Host "✅ Your application should be able to connect" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "1. Build your application: dotnet build" -ForegroundColor White
    Write-Host "2. Run your application: dotnet run" -ForegroundColor White
    Write-Host "3. Login with: admin / admin123" -ForegroundColor White
    
} catch {
    Write-Host "❌ Connection test failed!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Troubleshooting steps:" -ForegroundColor Yellow
    Write-Host "1. Check if PostgreSQL service is running" -ForegroundColor White
    Write-Host "2. Verify the password is 'abab'" -ForegroundColor White
    Write-Host "3. Confirm database 'TomGeneralTradingDB' exists" -ForegroundColor White
    Write-Host "4. Check Windows Firewall settings" -ForegroundColor White
}
