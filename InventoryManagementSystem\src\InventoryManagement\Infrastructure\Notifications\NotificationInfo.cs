using System;
using InventoryManagement.Events;

namespace InventoryManagement.Infrastructure.Notifications
{
    /// <summary>
    /// Information about a notification for UI display
    /// </summary>
    public class NotificationInfo
    {
        /// <summary>
        /// Unique identifier for the notification
        /// </summary>
        public Guid NotificationId { get; set; }
        
        /// <summary>
        /// Title of the notification
        /// </summary>
        public string Title { get; set; } = string.Empty;
        
        /// <summary>
        /// Message content of the notification
        /// </summary>
        public string Message { get; set; } = string.Empty;
        
        /// <summary>
        /// Type of notification
        /// </summary>
        public NotificationType Type { get; set; }
        
        /// <summary>
        /// When the notification was created
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;
        
        /// <summary>
        /// Whether the notification has an action button
        /// </summary>
        public bool HasAction { get; set; }
        
        /// <summary>
        /// Text for the action button
        /// </summary>
        public string? ActionText { get; set; }
        
        /// <summary>
        /// Whether the notification is persistent (doesn't auto-dismiss)
        /// </summary>
        public bool IsPersistent { get; set; }
        
        /// <summary>
        /// Duration in milliseconds before auto-dismiss (if not persistent)
        /// </summary>
        public int Duration { get; set; } = 5000;
        
        /// <summary>
        /// Priority level of the notification
        /// </summary>
        public Models.NotificationPriority Priority { get; set; } = Models.NotificationPriority.Normal;
        
        /// <summary>
        /// Optional data associated with the notification
        /// </summary>
        public object? Data { get; set; }
        
        /// <summary>
        /// Whether the notification has been dismissed
        /// </summary>
        public bool IsDismissed { get; set; }
        
        /// <summary>
        /// When the notification was dismissed
        /// </summary>
        public DateTime? DismissedAt { get; set; }
    }

    /// <summary>
    /// Event arguments for notification events
    /// </summary>
    public class NotificationEventArgs : EventArgs
    {
        /// <summary>
        /// The notification information
        /// </summary>
        public NotificationInfo Notification { get; set; }

        /// <summary>
        /// Creates new notification event arguments
        /// </summary>
        /// <param name="notification">The notification</param>
        public NotificationEventArgs(NotificationInfo notification)
        {
            Notification = notification ?? throw new ArgumentNullException(nameof(notification));
        }
    }
}
