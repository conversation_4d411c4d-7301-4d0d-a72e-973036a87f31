<Window x:Class="InventoryManagement.Views.DatabaseSetupProgressWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Database Setup" 
        Height="300" 
        Width="500"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        WindowStyle="SingleBorderWindow">
    
    <Window.Resources>
        <Style x:Key="ModernProgressBar" TargetType="ProgressBar">
            <Setter Property="Height" Value="20"/>
            <Setter Property="Background" Value="#F0F0F0"/>
            <Setter Property="Foreground" Value="#2196F3"/>
            <Setter Property="BorderBrush" Value="#DDDDDD"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>
        
        <Style x:Key="HeaderText" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>
        
        <Style x:Key="StatusText" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#666666"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,10"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
        </Style>
    </Window.Resources>
    
    <Grid Margin="30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <TextBlock Grid.Row="0" 
                   Text="Setting Up Database" 
                   Style="{StaticResource HeaderText}"/>
        
        <!-- Icon -->
        <Viewbox Grid.Row="1" 
                 Width="64" 
                 Height="64" 
                 HorizontalAlignment="Center"
                 Margin="0,0,0,20">
            <Canvas Width="24" Height="24">
                <Path Fill="#2196F3" 
                      Data="M12,3C7.58,3 4,4.79 4,7C4,9.21 7.58,11 12,11C16.42,11 20,9.21 20,7C20,4.79 16.42,3 12,3M4,9V12C4,14.21 7.58,16 12,16C16.42,16 20,14.21 20,12V9C20,11.21 16.42,13 12,13C7.58,13 4,11.21 4,9M4,14V17C4,19.21 7.58,21 12,21C16.42,21 20,19.21 20,17V14C20,16.21 16.42,18 12,18C7.58,18 4,16.21 4,14Z"/>
            </Canvas>
        </Viewbox>
        
        <!-- Status Text -->
        <TextBlock Grid.Row="2" 
                   x:Name="StatusTextBlock"
                   Text="Initializing setup..." 
                   Style="{StaticResource StatusText}"/>
        
        <!-- Progress Bar -->
        <ProgressBar Grid.Row="3" 
                     x:Name="SetupProgressBar"
                     Style="{StaticResource ModernProgressBar}"
                     Minimum="0" 
                     Maximum="100" 
                     Value="0"/>
        
        <!-- Progress Percentage -->
        <TextBlock Grid.Row="4" 
                   x:Name="PercentageTextBlock"
                   Text="0%"
                   FontSize="12"
                   Foreground="#999999"
                   HorizontalAlignment="Center"
                   Margin="0,10,0,0"/>
        
        <!-- Cancel Button (initially hidden) -->
        <Button Grid.Row="5" 
                x:Name="CancelButton"
                Content="Cancel"
                Width="100"
                Height="30"
                HorizontalAlignment="Center"
                Margin="0,20,0,0"
                Visibility="Collapsed"
                Click="CancelButton_Click">
            <Button.Style>
                <Style TargetType="Button">
                    <Setter Property="Background" Value="#F5F5F5"/>
                    <Setter Property="BorderBrush" Value="#DDDDDD"/>
                    <Setter Property="BorderThickness" Value="1"/>
                    <Setter Property="Foreground" Value="#333333"/>
                    <Setter Property="FontSize" Value="12"/>
                    <Setter Property="Cursor" Value="Hand"/>
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="Button">
                                <Border Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        CornerRadius="3">
                                    <ContentPresenter HorizontalAlignment="Center" 
                                                      VerticalAlignment="Center"/>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#E0E0E0"/>
                                    </Trigger>
                                    <Trigger Property="IsPressed" Value="True">
                                        <Setter Property="Background" Value="#D0D0D0"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </Button.Style>
        </Button>
    </Grid>
</Window>
