using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using InventoryManagement.Services;
using InventoryManagement.Services.Interfaces;
using InventoryManagement.DataAccess;
using InventoryManagement.Repositories;
using InventoryManagement.Infrastructure.Auth;
using InventoryManagement.Infrastructure.Validation;
using InventoryManagement.Infrastructure.Exceptions;
using InventoryManagement.ViewModels;
using InventoryManagement.Views;
using Microsoft.EntityFrameworkCore;
using System;

namespace InventoryManagement.Infrastructure.DependencyInjection
{
    /// <summary>
    /// Extension methods for configuring services in the DI container
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// Adds core application services to the service collection
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="configuration">The configuration</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddCoreServices(this IServiceCollection services, IConfiguration configuration)
        {
            // Add database context
            services.AddDbContext<ApplicationDbContext>(options =>
                options.UseNpgsql(configuration.GetConnectionString("DefaultConnection")));

            // Add repositories
            services.AddScoped(typeof(IRepository<>), typeof(Repository<>));
            services.AddScoped<IUserRepository, UserRepository>();
            services.AddScoped<IItemRepository, ItemRepository>();
            services.AddScoped<ITransactionRepository, TransactionRepository>();

            // Add core services
            services.AddScoped<IAuthService, AuthService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IItemService, ItemService>();
            services.AddScoped<ITransactionService, TransactionService>();
            services.AddScoped<INotificationService, NotificationService>();

            // Add infrastructure services
            services.AddScoped<IValidationService, ValidationService>();
            services.AddScoped<IErrorHandlingService, ErrorHandlingService>();

            return services;
        }

        /// <summary>
        /// Adds offline-optimized services to the service collection
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="configuration">The configuration</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddOfflineCoreServices(this IServiceCollection services, IConfiguration configuration)
        {
            // Add core services first
            services.AddCoreServices(configuration);

            // Add offline-specific services
            services.AddScoped<IOfflineOperationService, OfflineOperationService>();
            services.AddScoped<ISyncService, SyncService>();
            services.AddScoped<ICacheService, CacheService>();

            // Add enhanced notification service for offline mode
            services.AddScoped<Infrastructure.Notifications.OfflineNotificationService>();

            return services;
        }

        /// <summary>
        /// Adds view models to the service collection
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddViewModels(this IServiceCollection services)
        {
            // Add main view models
            services.AddTransient<MainWindowViewModel>();
            services.AddTransient<LoginViewModel>();
            services.AddTransient<DashboardViewModel>();
            services.AddTransient<InventoryViewModel>();
            services.AddTransient<SalesViewModel>();
            services.AddTransient<ReportsViewModel>();
            services.AddTransient<SettingsViewModel>();

            return services;
        }

        /// <summary>
        /// Adds views to the service collection
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddViews(this IServiceCollection services)
        {
            // Add main views
            services.AddTransient<MainWindow>();
            services.AddTransient<LoginWindow>();
            services.AddTransient<DashboardWindow>();
            services.AddTransient<InventoryWindow>();
            services.AddTransient<SalesWindow>();
            services.AddTransient<ReportsWindow>();
            services.AddTransient<SettingsWindow>();

            return services;
        }

        /// <summary>
        /// Adds validation services to the service collection
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddValidationServices(this IServiceCollection services)
        {
            // Add FluentValidation
            services.AddValidatorsFromAssemblyContaining<Program>();

            // Add custom validation services
            services.AddScoped<IValidationService, ValidationService>();
            services.AddScoped<IModelValidator, ModelValidator>();

            return services;
        }

        /// <summary>
        /// Adds logging services to the service collection
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="configuration">The configuration</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddLoggingServices(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddLogging(builder =>
            {
                builder.AddConfiguration(configuration.GetSection("Logging"));
                builder.AddConsole();
                builder.AddDebug();
                
                // Add file logging using Serilog
                builder.AddFile("Logs/app.log");
            });

            return services;
        }
    }
}
